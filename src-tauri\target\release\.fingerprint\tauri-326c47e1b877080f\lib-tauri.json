{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 1360074453433306088, "deps": [[40386456601120721, "percent_encoding", false, 6919515508513351699], [1200537532907108615, "url<PERSON><PERSON>n", false, 1849451693271148119], [2013030631243296465, "webview2_com", false, 2273413776130414245], [2671782512663819132, "tauri_utils", false, 8837733740804702724], [3150220818285335163, "url", false, 16112659691765740613], [3331586631144870129, "getrandom", false, 16089997552602294658], [4143744114649553716, "raw_window_handle", false, 8637821025176465503], [4494683389616423722, "muda", false, 12828489939739099045], [4919829919303820331, "serialize_to_javascript", false, 5572058891180121818], [5986029879202738730, "log", false, 1886841086015139440], [6089812615193535349, "tauri_runtime", false, 13927800302588223230], [7573826311589115053, "tauri_macros", false, 10677589703616573227], [9010263965687315507, "http", false, 4214057606062284768], [9689903380558560274, "serde", false, 11263741590175630023], [10229185211513642314, "mime", false, 6529572070011176069], [10806645703491011684, "thiserror", false, 352052627267944730], [11599800339996261026, "tauri_runtime_wry", false, 6753059070671896267], [11989259058781683633, "dunce", false, 12314797577974387159], [12393800526703971956, "tokio", false, 13570260893831848868], [12565293087094287914, "window_vibrancy", false, 7895874782696338991], [12986574360607194341, "serde_repr", false, 11175680278904399185], [13077543566650298139, "heck", false, 308202334380511133], [13625485746686963219, "anyhow", false, 7780976707766840329], [14039947826026167952, "build_script_build", false, 70273681997373412], [14585479307175734061, "windows", false, 7131688417191765701], [15367738274754116744, "serde_json", false, 1074150587366838657], [16928111194414003569, "dirs", false, 9898689469357270009], [17155886227862585100, "glob", false, 2878334200902487141]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-326c47e1b877080f\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}