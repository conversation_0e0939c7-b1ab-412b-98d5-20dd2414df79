{"$schema": "https://schema.tauri.app/config/2", "productName": "Tasbeeh", "version": "0.1.0", "identifier": "com.tasbeeh.app", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:1420", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "Tasbeeh - Digital Dhikr Counter", "width": 350, "height": 800, "minWidth": 350, "minHeight": 600, "maxWidth": 350, "resizable": false, "fullscreen": false, "decorations": false, "alwaysOnTop": false, "skipTaskbar": false, "transparent": true, "x": -350, "y": 0}], "security": {"csp": null}, "systemTray": {"iconPath": "icons/icon.png", "iconAsTemplate": true, "menuOnLeftClick": false}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}