{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 1369601567987815722, "path": 8191629354828218678, "deps": [[2671782512663819132, "tauri_utils", false, 272491262416792995], [4899080583175475170, "semver", false, 3404358045232739937], [6913375703034175521, "schemars", false, 8233784747826473377], [7170110829644101142, "json_patch", false, 9897603104506060233], [9689903380558560274, "serde", false, 8774692404286498438], [12714016054753183456, "tauri_winres", false, 5537813085533035436], [13077543566650298139, "heck", false, 5696005349256850969], [13475171727366188400, "cargo_toml", false, 16719828038683249240], [13625485746686963219, "anyhow", false, 15797132965273801366], [15367738274754116744, "serde_json", false, 8490591206872899158], [15609422047640926750, "toml", false, 14133604035600340924], [15622660310229662834, "walkdir", false, 2441827836749290769], [16928111194414003569, "dirs", false, 3206809977478992452], [17155886227862585100, "glob", false, 17613131663587803757]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-866b578f569cd465\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}