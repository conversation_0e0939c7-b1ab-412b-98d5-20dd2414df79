{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 986736018464402717, "deps": [[2671782512663819132, "tauri_utils", false, 272491262416792995], [3060637413840920116, "proc_macro2", false, 16066583218117435874], [3150220818285335163, "url", false, 958402791431327319], [4899080583175475170, "semver", false, 3404358045232739937], [4974441333307933176, "syn", false, 89758729515633307], [7170110829644101142, "json_patch", false, 9897603104506060233], [7392050791754369441, "ico", false, 650123037608259425], [8319709847752024821, "uuid", false, 16175218664815884304], [9556762810601084293, "brotli", false, 481607962408562776], [9689903380558560274, "serde", false, 8774692404286498438], [9857275760291862238, "sha2", false, 9144961544180662144], [10806645703491011684, "thiserror", false, 1177558230799811419], [12687914511023397207, "png", false, 12736901212058021618], [13077212702700853852, "base64", false, 10474274694659402274], [15367738274754116744, "serde_json", false, 8490591206872899158], [15622660310229662834, "walkdir", false, 2441827836749290769], [17990358020177143287, "quote", false, 14732800118198798024]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-3ff6439abe313b23\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}