{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 18023537391217044682, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 1849451693271148119], [3150220818285335163, "url", false, 16112659691765740613], [3191507132440681679, "serde_untagged", false, 3487769305551036847], [4071963112282141418, "serde_with", false, 10924413536803065353], [4899080583175475170, "semver", false, 18420754802156034503], [5986029879202738730, "log", false, 1886841086015139440], [6606131838865521726, "ctor", false, 12840013901492264485], [7170110829644101142, "json_patch", false, 8034223848107811149], [8319709847752024821, "uuid", false, 5022055615197639992], [9010263965687315507, "http", false, 4214057606062284768], [9451456094439810778, "regex", false, 11424543203286999370], [9556762810601084293, "brotli", false, 1350571076779530117], [9689903380558560274, "serde", false, 11263741590175630023], [10806645703491011684, "thiserror", false, 352052627267944730], [11989259058781683633, "dunce", false, 12314797577974387159], [13625485746686963219, "anyhow", false, 7780976707766840329], [15367738274754116744, "serde_json", false, 1074150587366838657], [15609422047640926750, "toml", false, 14075396409073776490], [15622660310229662834, "walkdir", false, 13921284143313169264], [15932120279885307830, "memchr", false, 17962990025594072345], [17146114186171651583, "infer", false, 14166682565892364766], [17155886227862585100, "glob", false, 2878334200902487141], [17186037756130803222, "phf", false, 15315166536082916070]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-f395d555664ce02f\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}