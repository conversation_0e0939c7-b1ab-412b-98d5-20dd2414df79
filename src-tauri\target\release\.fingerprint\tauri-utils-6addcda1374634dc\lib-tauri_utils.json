{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 1369601567987815722, "path": 18023537391217044682, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 17562275250127406592], [3060637413840920116, "proc_macro2", false, 16066583218117435874], [3150220818285335163, "url", false, 958402791431327319], [3191507132440681679, "serde_untagged", false, 12026554823497505640], [4071963112282141418, "serde_with", false, 6811553806838961665], [4899080583175475170, "semver", false, 3404358045232739937], [5986029879202738730, "log", false, 4303408508226223126], [6606131838865521726, "ctor", false, 12840013901492264485], [6913375703034175521, "schemars", false, 8233784747826473377], [7170110829644101142, "json_patch", false, 9897603104506060233], [8319709847752024821, "uuid", false, 16175218664815884304], [9010263965687315507, "http", false, 25171115672591742], [9451456094439810778, "regex", false, 14290175183411930193], [9556762810601084293, "brotli", false, 481607962408562776], [9689903380558560274, "serde", false, 8774692404286498438], [10806645703491011684, "thiserror", false, 1177558230799811419], [11655476559277113544, "cargo_metadata", false, 3503205921397727467], [11989259058781683633, "dunce", false, 5330220681132218088], [13625485746686963219, "anyhow", false, 15797132965273801366], [14232843520438415263, "html5ever", false, 15157272508775092020], [15088007382495681292, "kuchiki", false, 10838426735143128746], [15367738274754116744, "serde_json", false, 8490591206872899158], [15609422047640926750, "toml", false, 14133604035600340924], [15622660310229662834, "walkdir", false, 2441827836749290769], [15932120279885307830, "memchr", false, 14439834646267475477], [17146114186171651583, "infer", false, 16760269007647184290], [17155886227862585100, "glob", false, 17613131663587803757], [17186037756130803222, "phf", false, 12620994337103715818], [17990358020177143287, "quote", false, 14732800118198798024]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-6addcda1374634dc\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}