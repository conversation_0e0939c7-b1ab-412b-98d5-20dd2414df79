{"rustc": 1842507548689473721, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 8200121082086423072, "deps": [[2671782512663819132, "tauri_utils", false, 272491262416792995], [3060637413840920116, "proc_macro2", false, 16066583218117435874], [4974441333307933176, "syn", false, 89758729515633307], [13077543566650298139, "heck", false, 5696005349256850969], [14455244907590647360, "tauri_codegen", false, 7612430326213254251], [17990358020177143287, "quote", false, 14732800118198798024]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-da235bb0583fe395\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}