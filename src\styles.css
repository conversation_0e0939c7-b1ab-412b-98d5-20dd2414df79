/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON>', serif;
    background: linear-gradient(135deg, #0f4c75 0%, #3282b8 50%, #bbe1fa 100%);
    color: #ffffff;
    overflow: hidden;
    user-select: none;
    height: 100vh;
    width: 100vw;
}

/* Hidden badge styles */
.hidden-badge {
    position: fixed;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50% 0 0 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
}

.hidden-badge:hover {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.25);
}

.hidden-badge .total-count {
    font-size: 18px;
    font-weight: bold;
    color: #ffffff;
}

/* Main app container */
.main-app {
    position: fixed;
    right: 0;
    top: 0;
    width: 350px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    padding: 20px;
    overflow-y: auto;
    transition: transform 0.4s ease;
    z-index: 999;
}

.main-app.hidden {
    transform: translateX(100%);
}

/* Header styles */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.app-title {
    font-size: 28px;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.settings-icon {
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.settings-icon:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Dhikr container */
.dhikr-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 30px;
}

/* Dhikr item styles */
.dhikr-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    transition: all 0.3s ease;
}

.dhikr-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.dhikr-arabic {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 8px;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    direction: rtl;
}

.dhikr-transliteration {
    font-size: 16px;
    text-align: center;
    margin-bottom: 4px;
    color: #e8f4f8;
    font-style: italic;
}

.dhikr-translation {
    font-size: 14px;
    text-align: center;
    margin-bottom: 15px;
    color: #d1e7dd;
    opacity: 0.9;
}

/* Counter display */
.counter-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

.count {
    font-size: 24px;
    font-weight: bold;
    color: #ffffff;
    min-width: 40px;
    text-align: center;
    transition: all 0.3s ease;
}

.count.increment-animation {
    transform: scale(1.2);
    color: #4ade80;
}

/* Button styles */
.increment-btn, .reset-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: #ffffff;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.increment-btn:hover {
    background: rgba(76, 222, 128, 0.3);
    border-color: #4ade80;
    transform: scale(1.1);
}

.reset-btn:hover {
    background: rgba(248, 113, 113, 0.3);
    border-color: #f87171;
    transform: scale(1.1);
}

/* Total section */
.total-section {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.total-label {
    font-size: 18px;
    margin-bottom: 10px;
    color: #e8f4f8;
}

.total-count-display {
    font-size: 36px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.reset-all-btn {
    background: rgba(248, 113, 113, 0.2);
    border: 1px solid rgba(248, 113, 113, 0.4);
    border-radius: 25px;
    padding: 10px 20px;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reset-all-btn:hover {
    background: rgba(248, 113, 113, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(248, 113, 113, 0.3);
}

/* Settings panel */
.settings-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 350px;
    height: 100vh;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    padding: 20px;
    z-index: 1001;
    transition: transform 0.3s ease;
}

.settings-panel.hidden {
    transform: translateX(100%);
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-header h2 {
    color: #ffffff;
    font-size: 24px;
}

.close-settings {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.close-settings:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Settings content */
.settings-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-item label {
    color: #e8f4f8;
    font-size: 16px;
}

.setting-item select,
.setting-item input[type="checkbox"] {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 10px;
    color: #ffffff;
    font-size: 14px;
}

.setting-item select:focus {
    outline: none;
    border-color: #3282b8;
    box-shadow: 0 0 0 2px rgba(50, 130, 184, 0.3);
}

.setting-item button {
    background: rgba(50, 130, 184, 0.3);
    border: 1px solid rgba(50, 130, 184, 0.5);
    border-radius: 8px;
    padding: 10px 15px;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 10px;
}

.setting-item button:hover {
    background: rgba(50, 130, 184, 0.5);
    transform: translateY(-2px);
}

/* Utility classes */
.hidden {
    display: none !important;
}

/* Scrollbar styles */
.main-app::-webkit-scrollbar {
    width: 6px;
}

.main-app::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.main-app::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.main-app::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Responsive adjustments */
@media (max-height: 600px) {
    .dhikr-item {
        padding: 15px;
    }
    
    .dhikr-arabic {
        font-size: 20px;
    }
    
    .total-count-display {
        font-size: 28px;
    }
}
