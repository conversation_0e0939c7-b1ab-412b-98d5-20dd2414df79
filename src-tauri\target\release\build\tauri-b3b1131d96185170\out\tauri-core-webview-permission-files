["\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\clear_all_browsing_data.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\create_webview.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\create_webview_window.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\get_all_webviews.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\internal_toggle_devtools.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\print.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\reparent.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_auto_resize.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_background_color.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_focus.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_position.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_size.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_zoom.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\webview_close.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\webview_hide.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\webview_position.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\webview_show.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\commands\\webview_size.toml", "\\\\?\\C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\VscodeProject\\Sabe7\\src-tauri\\target\\release\\build\\tauri-b3b1131d96185170\\out\\permissions\\webview\\autogenerated\\default.toml"]