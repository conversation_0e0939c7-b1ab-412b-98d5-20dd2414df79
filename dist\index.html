<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tasbeeh - Digital Dhikr Counter</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
  <script type="module" crossorigin src="/assets/index-BDgnLcn5.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/index-BwZeahcP.css">
</head>
<body>
    <div id="app">
        <!-- Hidden State - Circular Counter Badge -->
        <div id="hidden-badge" class="hidden-badge">
            <div class="total-count">0</div>
        </div>

        <!-- Main App Container -->
        <div id="main-app" class="main-app">
            <!-- Header -->
            <div class="app-header">
                <h1 class="app-title">تسبيح</h1>
                <div class="settings-icon" id="settings-btn">⚙️</div>
            </div>

            <!-- Dhikr Counters -->
            <div class="dhikr-container">
                <!-- La ilaha illa Allah -->
                <div class="dhikr-item" data-dhikr="1">
                    <div class="dhikr-arabic">لا إلَهَ إلاَّ اللهُ</div>
                    <div class="dhikr-transliteration">La ilaha illa Allah</div>
                    <div class="dhikr-translation">"There is no god but Allah"</div>
                    <div class="counter-display">
                        <span class="count" id="count-1">0</span>
                        <button class="increment-btn" data-dhikr="1">+</button>
                        <button class="reset-btn" data-dhikr="1">↻</button>
                    </div>
                </div>

                <!-- Allahu Akbar -->
                <div class="dhikr-item" data-dhikr="2">
                    <div class="dhikr-arabic">اللهُ أكْبَرُ</div>
                    <div class="dhikr-transliteration">Allahu Akbar</div>
                    <div class="dhikr-translation">"Allah is the Greatest"</div>
                    <div class="counter-display">
                        <span class="count" id="count-2">0</span>
                        <button class="increment-btn" data-dhikr="2">+</button>
                        <button class="reset-btn" data-dhikr="2">↻</button>
                    </div>
                </div>

                <!-- Alhamdulillah -->
                <div class="dhikr-item" data-dhikr="3">
                    <div class="dhikr-arabic">الْحَمْدُ للهِ</div>
                    <div class="dhikr-transliteration">Alhamdulillah</div>
                    <div class="dhikr-translation">"Praise be to Allah"</div>
                    <div class="counter-display">
                        <span class="count" id="count-3">0</span>
                        <button class="increment-btn" data-dhikr="3">+</button>
                        <button class="reset-btn" data-dhikr="3">↻</button>
                    </div>
                </div>

                <!-- SubhanAllah -->
                <div class="dhikr-item" data-dhikr="4">
                    <div class="dhikr-arabic">سُبْحَانَ اللهِ</div>
                    <div class="dhikr-transliteration">SubhanAllah</div>
                    <div class="dhikr-translation">"Glory be to Allah"</div>
                    <div class="counter-display">
                        <span class="count" id="count-4">0</span>
                        <button class="increment-btn" data-dhikr="4">+</button>
                        <button class="reset-btn" data-dhikr="4">↻</button>
                    </div>
                </div>
            </div>

            <!-- Total Counter -->
            <div class="total-section">
                <div class="total-label">Total Dhikr</div>
                <div class="total-count-display" id="total-count">0</div>
                <button class="reset-all-btn" id="reset-all">Reset All</button>
            </div>
        </div>

        <!-- Settings Panel -->
        <div id="settings-panel" class="settings-panel hidden">
            <div class="settings-header">
                <h2>Settings</h2>
                <button class="close-settings" id="close-settings">×</button>
            </div>
            <div class="settings-content">
                <div class="setting-item">
                    <label for="reminder-interval">Reminder Interval (minutes):</label>
                    <select id="reminder-interval">
                        <option value="0">Disabled</option>
                        <option value="5">5 minutes</option>
                        <option value="10">10 minutes</option>
                        <option value="15">15 minutes</option>
                        <option value="30">30 minutes</option>
                        <option value="60">60 minutes</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>
                        <input type="checkbox" id="always-on-top"> Always on top
                    </label>
                </div>
                <div class="setting-item">
                    <button id="export-data">Export Data</button>
                    <button id="import-data">Import Data</button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
