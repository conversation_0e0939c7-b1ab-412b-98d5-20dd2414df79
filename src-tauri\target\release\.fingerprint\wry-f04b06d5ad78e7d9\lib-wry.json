{"rustc": 1842507548689473721, "features": "[\"drag-drop\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "target": 2463569863749872413, "profile": 1554061257607164671, "path": 787390226201927088, "deps": [[2013030631243296465, "webview2_com", false, 2273413776130414245], [3334271191048661305, "windows_version", false, 11267922080368418950], [3722963349756955755, "once_cell", false, 16169173815255071269], [4143744114649553716, "raw_window_handle", false, 8637821025176465503], [5628259161083531273, "windows_core", false, 19906638450218161], [7606335748176206944, "dpi", false, 16165669722731118951], [9010263965687315507, "http", false, 4214057606062284768], [9141053277961803901, "build_script_build", false, 14413499366738769782], [10806645703491011684, "thiserror", false, 352052627267944730], [11989259058781683633, "dunce", false, 12314797577974387159], [14585479307175734061, "windows", false, 7131688417191765701], [16727543399706004146, "cookie", false, 2676065732424466422]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\wry-f04b06d5ad78e7d9\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}