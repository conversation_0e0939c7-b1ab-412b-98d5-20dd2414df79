import { invoke } from '@tauri-apps/api/core'
import { listen } from '@tauri-apps/api/event'
import { getCurrentWindow } from '@tauri-apps/api/window'

// Application state
let dhikrCounts = {
    1: 0, // La ilaha illa Allah
    2: 0, // <PERSON><PERSON>
    3: 0, // Alhamdulillah
    4: 0  // SubhanAllah
}

let isHidden = false
let hideTimeout = null
let reminderInterval = null
let settings = {
    reminderIntervalMinutes: 0,
    alwaysOnTop: false
}

// DOM elements
const hiddenBadge = document.getElementById('hidden-badge')
const mainApp = document.getElementById('main-app')
const settingsPanel = document.getElementById('settings-panel')
const totalCountDisplay = document.getElementById('total-count')

// Initialize the application
async function init() {
    await loadData()
    setupEventListeners()
    setupKeyboardShortcuts()
    updateDisplay()
    setupAutoHide()
    loadSettings()
}

// Load data from backend
async function loadData() {
    try {
        const data = await invoke('load_dhikr_data')
        if (data) {
            dhikrCounts = data.counts || dhikrCounts
            settings = { ...settings, ...data.settings }
        }
    } catch (error) {
        console.log('No existing data found, starting fresh')
    }
}

// Save data to backend
async function saveData() {
    try {
        await invoke('save_dhikr_data', {
            data: {
                counts: dhikrCounts,
                settings: settings
            }
        })
    } catch (error) {
        console.error('Failed to save data:', error)
    }
}

// Setup event listeners
function setupEventListeners() {
    // Increment buttons
    document.querySelectorAll('.increment-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const dhikrId = parseInt(e.target.dataset.dhikr)
            incrementDhikr(dhikrId)
        })
    })

    // Reset buttons
    document.querySelectorAll('.reset-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const dhikrId = parseInt(e.target.dataset.dhikr)
            resetDhikr(dhikrId)
        })
    })

    // Reset all button
    document.getElementById('reset-all').addEventListener('click', resetAll)

    // Settings
    document.getElementById('settings-btn').addEventListener('click', openSettings)
    document.getElementById('close-settings').addEventListener('click', closeSettings)
    
    // Settings controls
    document.getElementById('reminder-interval').addEventListener('change', updateReminderInterval)
    document.getElementById('always-on-top').addEventListener('change', updateAlwaysOnTop)
    document.getElementById('export-data').addEventListener('click', exportData)
    document.getElementById('import-data').addEventListener('click', importData)

    // Mouse events for auto-hide
    document.addEventListener('mouseenter', showApp)
    document.addEventListener('mouseleave', scheduleHide)
}

// Setup keyboard shortcuts
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        if (e.key >= '1' && e.key <= '4') {
            const dhikrId = parseInt(e.key)
            incrementDhikr(dhikrId)
            e.preventDefault()
        }
    })
}

// Increment dhikr counter
function incrementDhikr(dhikrId) {
    dhikrCounts[dhikrId]++
    updateDisplay()
    saveData()
    
    // Show visual feedback
    const countElement = document.getElementById(`count-${dhikrId}`)
    countElement.classList.add('increment-animation')
    setTimeout(() => countElement.classList.remove('increment-animation'), 300)
}

// Reset individual dhikr
function resetDhikr(dhikrId) {
    dhikrCounts[dhikrId] = 0
    updateDisplay()
    saveData()
}

// Reset all counters
function resetAll() {
    if (confirm('Are you sure you want to reset all counters?')) {
        dhikrCounts = { 1: 0, 2: 0, 3: 0, 4: 0 }
        updateDisplay()
        saveData()
    }
}

// Update display
function updateDisplay() {
    // Update individual counters
    for (let i = 1; i <= 4; i++) {
        document.getElementById(`count-${i}`).textContent = dhikrCounts[i]
    }
    
    // Update total
    const total = Object.values(dhikrCounts).reduce((sum, count) => sum + count, 0)
    totalCountDisplay.textContent = total
    hiddenBadge.querySelector('.total-count').textContent = total
}

// Auto-hide functionality
function setupAutoHide() {
    // Initially hide after 2 seconds
    setTimeout(() => {
        hideApp()
    }, 2000)
}

function showApp() {
    if (hideTimeout) {
        clearTimeout(hideTimeout)
        hideTimeout = null
    }
    
    if (isHidden) {
        isHidden = false
        hiddenBadge.classList.add('hidden')
        mainApp.classList.remove('hidden')
    }
}

function scheduleHide() {
    if (hideTimeout) {
        clearTimeout(hideTimeout)
    }
    
    hideTimeout = setTimeout(() => {
        hideApp()
    }, 800)
}

function hideApp() {
    if (!isHidden && !settingsPanel.classList.contains('hidden')) {
        return // Don't hide if settings are open
    }
    
    isHidden = true
    hiddenBadge.classList.remove('hidden')
    mainApp.classList.add('hidden')
}

// Settings functions
function openSettings() {
    settingsPanel.classList.remove('hidden')
}

function closeSettings() {
    settingsPanel.classList.add('hidden')
}

function loadSettings() {
    document.getElementById('reminder-interval').value = settings.reminderIntervalMinutes
    document.getElementById('always-on-top').checked = settings.alwaysOnTop
    
    if (settings.reminderIntervalMinutes > 0) {
        setupReminders()
    }
}

function updateReminderInterval(e) {
    settings.reminderIntervalMinutes = parseInt(e.target.value)
    saveData()
    
    if (reminderInterval) {
        clearInterval(reminderInterval)
        reminderInterval = null
    }
    
    if (settings.reminderIntervalMinutes > 0) {
        setupReminders()
    }
}

function updateAlwaysOnTop(e) {
    settings.alwaysOnTop = e.target.checked
    saveData()
    
    // Update window always on top
    getCurrentWindow().setAlwaysOnTop(settings.alwaysOnTop)
}

function setupReminders() {
    if (reminderInterval) {
        clearInterval(reminderInterval)
    }
    
    reminderInterval = setInterval(() => {
        showReminder()
    }, settings.reminderIntervalMinutes * 60 * 1000)
}

function showReminder() {
    // This will be implemented with Tauri notifications
    invoke('show_reminder_notification')
}

function exportData() {
    const dataStr = JSON.stringify({ counts: dhikrCounts, settings }, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'tasbeeh-data.json'
    a.click()
    URL.revokeObjectURL(url)
}

function importData() {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = (e) => {
        const file = e.target.files[0]
        if (file) {
            const reader = new FileReader()
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result)
                    if (data.counts) {
                        dhikrCounts = data.counts
                    }
                    if (data.settings) {
                        settings = { ...settings, ...data.settings }
                    }
                    updateDisplay()
                    loadSettings()
                    saveData()
                    alert('Data imported successfully!')
                } catch (error) {
                    alert('Invalid file format')
                }
            }
            reader.readAsText(file)
        }
    }
    input.click()
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', init)

// Handle window events
window.addEventListener('beforeunload', saveData)
