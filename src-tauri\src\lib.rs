use std::fs;
use std::path::PathBuf;
use serde::{Deserialize, Serialize};
use tauri::{command, <PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, State, Window};
use std::collections::HashMap;

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct DhikrData {
    pub counts: HashMap<u8, u32>,
    pub settings: Settings,
}

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct Settings {
    pub reminder_interval_minutes: u32,
    pub always_on_top: bool,
}

impl Default for DhikrData {
    fn default() -> Self {
        let mut counts = HashMap::new();
        counts.insert(1, 0);
        counts.insert(2, 0);
        counts.insert(3, 0);
        counts.insert(4, 0);

        Self {
            counts,
            settings: Settings {
                reminder_interval_minutes: 0,
                always_on_top: false,
            },
        }
    }
}

fn get_data_file_path(app_handle: &AppHandle) -> Result<PathBuf, Box<dyn std::error::Error>> {
    let app_data_dir = app_handle.path().app_data_dir()?;
    fs::create_dir_all(&app_data_dir)?;
    Ok(app_data_dir.join("tasbeeh_data.json"))
}

#[command]
async fn load_dhikr_data(app_handle: AppHandle) -> Result<DhikrData, String> {
    let data_path = get_data_file_path(&app_handle).map_err(|e| e.to_string())?;

    if data_path.exists() {
        let data_str = fs::read_to_string(data_path).map_err(|e| e.to_string())?;
        let data: DhikrData = serde_json::from_str(&data_str).map_err(|e| e.to_string())?;
        Ok(data)
    } else {
        Ok(DhikrData::default())
    }
}

#[command]
async fn save_dhikr_data(app_handle: AppHandle, data: DhikrData) -> Result<(), String> {
    let data_path = get_data_file_path(&app_handle).map_err(|e| e.to_string())?;
    let data_str = serde_json::to_string_pretty(&data).map_err(|e| e.to_string())?;
    fs::write(data_path, data_str).map_err(|e| e.to_string())?;
    Ok(())
}

#[command]
async fn show_reminder_notification(app_handle: AppHandle) -> Result<(), String> {
    let notification_messages = vec![
        "Time for dhikr - Remember Allah ﷻ",
        "Take a moment for spiritual reflection",
        "اذكروا الله كثيراً - Remember Allah often",
        "A moment of dhikr brings peace to the heart",
    ];

    let message = notification_messages[rand::random::<usize>() % notification_messages.len()];

    // For now, we'll use a simple approach without the notification plugin
    // This can be enhanced later with proper system notifications
    println!("Notification: {} - {}", "Tasbeeh Reminder", message);

    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .setup(|app| {
            if cfg!(debug_assertions) {
                app.handle().plugin(
                    tauri_plugin_log::Builder::default()
                        .level(log::LevelFilter::Info)
                        .build(),
                )?;
            }
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            load_dhikr_data,
            save_dhikr_data,
            show_reminder_notification
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
