{"rustc": 1842507548689473721, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 1369601567987815722, "path": 12955409895628973259, "deps": [[3150220818285335163, "url", false, 958402791431327319], [6913375703034175521, "build_script_build", false, 2828123787600181800], [8319709847752024821, "uuid1", false, 16175218664815884304], [9122563107207267705, "dyn_clone", false, 5946433833199500212], [9689903380558560274, "serde", false, 8774692404286498438], [14923790796823607459, "indexmap", false, 18366191560998587084], [15367738274754116744, "serde_json", false, 8490591206872899158], [16071897500792579091, "schemars_derive", false, 3986638675878352245]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\schemars-f836274ba4e36a15\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}