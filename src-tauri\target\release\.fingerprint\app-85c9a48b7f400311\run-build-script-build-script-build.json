{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 15882450663525590083], [14039947826026167952, "build_script_build", false, 70273681997373412], [8324462083842905811, "build_script_build", false, 1038651888589784314]], "local": [{"RerunIfChanged": {"output": "release\\build\\app-85c9a48b7f400311\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}