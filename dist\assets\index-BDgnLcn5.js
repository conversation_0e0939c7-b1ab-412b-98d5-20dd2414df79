(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))l(s);new MutationObserver(s=>{for(const a of s)if(a.type==="childList")for(const r of a.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&l(r)}).observe(document,{childList:!0,subtree:!0});function n(s){const a={};return s.integrity&&(a.integrity=s.integrity),s.referrerPolicy&&(a.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?a.credentials="include":s.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function l(s){if(s.ep)return;s.ep=!0;const a=n(s);fetch(s.href,a)}})();function V(t,e,n,l){if(typeof e=="function"?t!==e||!l:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?l:n==="a"?l.call(t):l?l.value:e.get(t)}function j(t,e,n,l,s){if(typeof e=="function"?t!==e||!0:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(t,n),n}var v;const c="__TAURI_TO_IPC_KEY__";function q(t,e=!1){return window.__TAURI_INTERNALS__.transformCallback(t,e)}async function i(t,e={},n){return window.__TAURI_INTERNALS__.invoke(t,e,n)}class J{get rid(){return V(this,v,"f")}constructor(e){v.set(this,void 0),j(this,v,e)}async close(){return i("plugin:resources|close",{rid:this.rid})}}v=new WeakMap;var u;(function(t){t.WINDOW_RESIZED="tauri://resize",t.WINDOW_MOVED="tauri://move",t.WINDOW_CLOSE_REQUESTED="tauri://close-requested",t.WINDOW_DESTROYED="tauri://destroyed",t.WINDOW_FOCUS="tauri://focus",t.WINDOW_BLUR="tauri://blur",t.WINDOW_SCALE_FACTOR_CHANGED="tauri://scale-change",t.WINDOW_THEME_CHANGED="tauri://theme-changed",t.WINDOW_CREATED="tauri://window-created",t.WEBVIEW_CREATED="tauri://webview-created",t.DRAG_ENTER="tauri://drag-enter",t.DRAG_OVER="tauri://drag-over",t.DRAG_DROP="tauri://drag-drop",t.DRAG_LEAVE="tauri://drag-leave"})(u||(u={}));async function z(t,e){window.__TAURI_EVENT_PLUGIN_INTERNALS__.unregisterListener(t,e),await i("plugin:event|unlisten",{event:t,eventId:e})}async function N(t,e,n){var l;const s=typeof(n==null?void 0:n.target)=="string"?{kind:"AnyLabel",label:n.target}:(l=n==null?void 0:n.target)!==null&&l!==void 0?l:{kind:"Any"};return i("plugin:event|listen",{event:t,target:s,handler:q(e)}).then(a=>async()=>z(t,a))}async function $(t,e,n){return N(t,l=>{z(t,l.id),e(l)},n)}async function K(t,e){await i("plugin:event|emit",{event:t,payload:e})}async function Z(t,e,n){await i("plugin:event|emit_to",{target:typeof t=="string"?{kind:"AnyLabel",label:t}:t,event:e,payload:n})}class x{constructor(...e){this.type="Logical",e.length===1?"Logical"in e[0]?(this.width=e[0].Logical.width,this.height=e[0].Logical.height):(this.width=e[0].width,this.height=e[0].height):(this.width=e[0],this.height=e[1])}toPhysical(e){return new g(this.width*e,this.height*e)}[c](){return{width:this.width,height:this.height}}toJSON(){return this[c]()}}class g{constructor(...e){this.type="Physical",e.length===1?"Physical"in e[0]?(this.width=e[0].Physical.width,this.height=e[0].Physical.height):(this.width=e[0].width,this.height=e[0].height):(this.width=e[0],this.height=e[1])}toLogical(e){return new x(this.width/e,this.height/e)}[c](){return{width:this.width,height:this.height}}toJSON(){return this[c]()}}class y{constructor(e){this.size=e}toLogical(e){return this.size instanceof x?this.size:this.size.toLogical(e)}toPhysical(e){return this.size instanceof g?this.size:this.size.toPhysical(e)}[c](){return{[`${this.size.type}`]:{width:this.size.width,height:this.size.height}}}toJSON(){return this[c]()}}class T{constructor(...e){this.type="Logical",e.length===1?"Logical"in e[0]?(this.x=e[0].Logical.x,this.y=e[0].Logical.y):(this.x=e[0].x,this.y=e[0].y):(this.x=e[0],this.y=e[1])}toPhysical(e){return new h(this.x*e,this.y*e)}[c](){return{x:this.x,y:this.y}}toJSON(){return this[c]()}}class h{constructor(...e){this.type="Physical",e.length===1?"Physical"in e[0]?(this.x=e[0].Physical.x,this.y=e[0].Physical.y):(this.x=e[0].x,this.y=e[0].y):(this.x=e[0],this.y=e[1])}toLogical(e){return new T(this.x/e,this.y/e)}[c](){return{x:this.x,y:this.y}}toJSON(){return this[c]()}}class f{constructor(e){this.position=e}toLogical(e){return this.position instanceof T?this.position:this.position.toLogical(e)}toPhysical(e){return this.position instanceof h?this.position:this.position.toPhysical(e)}[c](){return{[`${this.position.type}`]:{x:this.position.x,y:this.position.y}}}toJSON(){return this[c]()}}class m extends J{constructor(e){super(e)}static async new(e,n,l){return i("plugin:image|new",{rgba:I(e),width:n,height:l}).then(s=>new m(s))}static async fromBytes(e){return i("plugin:image|from_bytes",{bytes:I(e)}).then(n=>new m(n))}static async fromPath(e){return i("plugin:image|from_path",{path:e}).then(n=>new m(n))}async rgba(){return i("plugin:image|rgba",{rid:this.rid}).then(e=>new Uint8Array(e))}async size(){return i("plugin:image|size",{rid:this.rid})}}function I(t){return t==null?null:typeof t=="string"?t:t instanceof m?t.rid:t}var O;(function(t){t[t.Critical=1]="Critical",t[t.Informational=2]="Informational"})(O||(O={}));class Q{constructor(e){this._preventDefault=!1,this.event=e.event,this.id=e.id}preventDefault(){this._preventDefault=!0}isPreventDefault(){return this._preventDefault}}var S;(function(t){t.None="none",t.Normal="normal",t.Indeterminate="indeterminate",t.Paused="paused",t.Error="error"})(S||(S={}));function P(){return new B(window.__TAURI_INTERNALS__.metadata.currentWindow.label,{skip:!0})}async function D(){return i("plugin:window|get_all_windows").then(t=>t.map(e=>new B(e,{skip:!0})))}const E=["tauri://created","tauri://error"];class B{constructor(e,n={}){var l;this.label=e,this.listeners=Object.create(null),n!=null&&n.skip||i("plugin:window|create",{options:{...n,parent:typeof n.parent=="string"?n.parent:(l=n.parent)===null||l===void 0?void 0:l.label,label:e}}).then(async()=>this.emit("tauri://created")).catch(async s=>this.emit("tauri://error",s))}static async getByLabel(e){var n;return(n=(await D()).find(l=>l.label===e))!==null&&n!==void 0?n:null}static getCurrent(){return P()}static async getAll(){return D()}static async getFocusedWindow(){for(const e of await D())if(await e.isFocused())return e;return null}async listen(e,n){return this._handleTauriEvent(e,n)?()=>{const l=this.listeners[e];l.splice(l.indexOf(n),1)}:N(e,n,{target:{kind:"Window",label:this.label}})}async once(e,n){return this._handleTauriEvent(e,n)?()=>{const l=this.listeners[e];l.splice(l.indexOf(n),1)}:$(e,n,{target:{kind:"Window",label:this.label}})}async emit(e,n){if(E.includes(e)){for(const l of this.listeners[e]||[])l({event:e,id:-1,payload:n});return}return K(e,n)}async emitTo(e,n,l){if(E.includes(n)){for(const s of this.listeners[n]||[])s({event:n,id:-1,payload:l});return}return Z(e,n,l)}_handleTauriEvent(e,n){return E.includes(e)?(e in this.listeners?this.listeners[e].push(n):this.listeners[e]=[n],!0):!1}async scaleFactor(){return i("plugin:window|scale_factor",{label:this.label})}async innerPosition(){return i("plugin:window|inner_position",{label:this.label}).then(e=>new h(e))}async outerPosition(){return i("plugin:window|outer_position",{label:this.label}).then(e=>new h(e))}async innerSize(){return i("plugin:window|inner_size",{label:this.label}).then(e=>new g(e))}async outerSize(){return i("plugin:window|outer_size",{label:this.label}).then(e=>new g(e))}async isFullscreen(){return i("plugin:window|is_fullscreen",{label:this.label})}async isMinimized(){return i("plugin:window|is_minimized",{label:this.label})}async isMaximized(){return i("plugin:window|is_maximized",{label:this.label})}async isFocused(){return i("plugin:window|is_focused",{label:this.label})}async isDecorated(){return i("plugin:window|is_decorated",{label:this.label})}async isResizable(){return i("plugin:window|is_resizable",{label:this.label})}async isMaximizable(){return i("plugin:window|is_maximizable",{label:this.label})}async isMinimizable(){return i("plugin:window|is_minimizable",{label:this.label})}async isClosable(){return i("plugin:window|is_closable",{label:this.label})}async isVisible(){return i("plugin:window|is_visible",{label:this.label})}async title(){return i("plugin:window|title",{label:this.label})}async theme(){return i("plugin:window|theme",{label:this.label})}async isAlwaysOnTop(){return i("plugin:window|is_always_on_top",{label:this.label})}async center(){return i("plugin:window|center",{label:this.label})}async requestUserAttention(e){let n=null;return e&&(e===O.Critical?n={type:"Critical"}:n={type:"Informational"}),i("plugin:window|request_user_attention",{label:this.label,value:n})}async setResizable(e){return i("plugin:window|set_resizable",{label:this.label,value:e})}async setEnabled(e){return i("plugin:window|set_enabled",{label:this.label,value:e})}async isEnabled(){return i("plugin:window|is_enabled",{label:this.label})}async setMaximizable(e){return i("plugin:window|set_maximizable",{label:this.label,value:e})}async setMinimizable(e){return i("plugin:window|set_minimizable",{label:this.label,value:e})}async setClosable(e){return i("plugin:window|set_closable",{label:this.label,value:e})}async setTitle(e){return i("plugin:window|set_title",{label:this.label,value:e})}async maximize(){return i("plugin:window|maximize",{label:this.label})}async unmaximize(){return i("plugin:window|unmaximize",{label:this.label})}async toggleMaximize(){return i("plugin:window|toggle_maximize",{label:this.label})}async minimize(){return i("plugin:window|minimize",{label:this.label})}async unminimize(){return i("plugin:window|unminimize",{label:this.label})}async show(){return i("plugin:window|show",{label:this.label})}async hide(){return i("plugin:window|hide",{label:this.label})}async close(){return i("plugin:window|close",{label:this.label})}async destroy(){return i("plugin:window|destroy",{label:this.label})}async setDecorations(e){return i("plugin:window|set_decorations",{label:this.label,value:e})}async setShadow(e){return i("plugin:window|set_shadow",{label:this.label,value:e})}async setEffects(e){return i("plugin:window|set_effects",{label:this.label,value:e})}async clearEffects(){return i("plugin:window|set_effects",{label:this.label,value:null})}async setAlwaysOnTop(e){return i("plugin:window|set_always_on_top",{label:this.label,value:e})}async setAlwaysOnBottom(e){return i("plugin:window|set_always_on_bottom",{label:this.label,value:e})}async setContentProtected(e){return i("plugin:window|set_content_protected",{label:this.label,value:e})}async setSize(e){return i("plugin:window|set_size",{label:this.label,value:e instanceof y?e:new y(e)})}async setMinSize(e){return i("plugin:window|set_min_size",{label:this.label,value:e instanceof y?e:e?new y(e):null})}async setMaxSize(e){return i("plugin:window|set_max_size",{label:this.label,value:e instanceof y?e:e?new y(e):null})}async setSizeConstraints(e){function n(l){return l?{Logical:l}:null}return i("plugin:window|set_size_constraints",{label:this.label,value:{minWidth:n(e==null?void 0:e.minWidth),minHeight:n(e==null?void 0:e.minHeight),maxWidth:n(e==null?void 0:e.maxWidth),maxHeight:n(e==null?void 0:e.maxHeight)}})}async setPosition(e){return i("plugin:window|set_position",{label:this.label,value:e instanceof f?e:new f(e)})}async setFullscreen(e){return i("plugin:window|set_fullscreen",{label:this.label,value:e})}async setFocus(){return i("plugin:window|set_focus",{label:this.label})}async setIcon(e){return i("plugin:window|set_icon",{label:this.label,value:I(e)})}async setSkipTaskbar(e){return i("plugin:window|set_skip_taskbar",{label:this.label,value:e})}async setCursorGrab(e){return i("plugin:window|set_cursor_grab",{label:this.label,value:e})}async setCursorVisible(e){return i("plugin:window|set_cursor_visible",{label:this.label,value:e})}async setCursorIcon(e){return i("plugin:window|set_cursor_icon",{label:this.label,value:e})}async setBackgroundColor(e){return i("plugin:window|set_background_color",{color:e})}async setCursorPosition(e){return i("plugin:window|set_cursor_position",{label:this.label,value:e instanceof f?e:new f(e)})}async setIgnoreCursorEvents(e){return i("plugin:window|set_ignore_cursor_events",{label:this.label,value:e})}async startDragging(){return i("plugin:window|start_dragging",{label:this.label})}async startResizeDragging(e){return i("plugin:window|start_resize_dragging",{label:this.label,value:e})}async setBadgeCount(e){return i("plugin:window|set_badge_count",{label:this.label,value:e})}async setBadgeLabel(e){return i("plugin:window|set_badge_label",{label:this.label,value:e})}async setOverlayIcon(e){return i("plugin:window|set_overlay_icon",{label:this.label,value:e?I(e):void 0})}async setProgressBar(e){return i("plugin:window|set_progress_bar",{label:this.label,value:e})}async setVisibleOnAllWorkspaces(e){return i("plugin:window|set_visible_on_all_workspaces",{label:this.label,value:e})}async setTitleBarStyle(e){return i("plugin:window|set_title_bar_style",{label:this.label,value:e})}async setTheme(e){return i("plugin:window|set_theme",{label:this.label,value:e})}async onResized(e){return this.listen(u.WINDOW_RESIZED,n=>{n.payload=new g(n.payload),e(n)})}async onMoved(e){return this.listen(u.WINDOW_MOVED,n=>{n.payload=new h(n.payload),e(n)})}async onCloseRequested(e){return this.listen(u.WINDOW_CLOSE_REQUESTED,async n=>{const l=new Q(n);await e(l),l.isPreventDefault()||await this.destroy()})}async onDragDropEvent(e){const n=await this.listen(u.DRAG_ENTER,r=>{e({...r,payload:{type:"enter",paths:r.payload.paths,position:new h(r.payload.position)}})}),l=await this.listen(u.DRAG_OVER,r=>{e({...r,payload:{type:"over",position:new h(r.payload.position)}})}),s=await this.listen(u.DRAG_DROP,r=>{e({...r,payload:{type:"drop",paths:r.payload.paths,position:new h(r.payload.position)}})}),a=await this.listen(u.DRAG_LEAVE,r=>{e({...r,payload:{type:"leave"}})});return()=>{n(),s(),l(),a()}}async onFocusChanged(e){const n=await this.listen(u.WINDOW_FOCUS,s=>{e({...s,payload:!0})}),l=await this.listen(u.WINDOW_BLUR,s=>{e({...s,payload:!1})});return()=>{n(),l()}}async onScaleChanged(e){return this.listen(u.WINDOW_SCALE_FACTOR_CHANGED,e)}async onThemeChanged(e){return this.listen(u.WINDOW_THEME_CHANGED,e)}}var W;(function(t){t.Disabled="disabled",t.Throttle="throttle",t.Suspend="suspend"})(W||(W={}));var R;(function(t){t.AppearanceBased="appearanceBased",t.Light="light",t.Dark="dark",t.MediumLight="mediumLight",t.UltraDark="ultraDark",t.Titlebar="titlebar",t.Selection="selection",t.Menu="menu",t.Popover="popover",t.Sidebar="sidebar",t.HeaderView="headerView",t.Sheet="sheet",t.WindowBackground="windowBackground",t.HudWindow="hudWindow",t.FullScreenUI="fullScreenUI",t.Tooltip="tooltip",t.ContentBackground="contentBackground",t.UnderWindowBackground="underWindowBackground",t.UnderPageBackground="underPageBackground",t.Mica="mica",t.Blur="blur",t.Acrylic="acrylic",t.Tabbed="tabbed",t.TabbedDark="tabbedDark",t.TabbedLight="tabbedLight"})(R||(R={}));var C;(function(t){t.FollowsWindowActiveState="followsWindowActiveState",t.Active="active",t.Inactive="inactive"})(C||(C={}));let d={1:0,2:0,3:0,4:0},L=!1,p=null,b=null,o={reminderIntervalMinutes:0,alwaysOnTop:!1};const A=document.getElementById("hidden-badge"),M=document.getElementById("main-app"),k=document.getElementById("settings-panel"),Y=document.getElementById("total-count");async function X(){await ee(),te(),ne(),_(),se(),G()}async function ee(){try{const t=await i("load_dhikr_data");t&&(d=t.counts||d,o={...o,...t.settings})}catch{console.log("No existing data found, starting fresh")}}async function w(){try{await i("save_dhikr_data",{data:{counts:d,settings:o}})}catch(t){console.error("Failed to save data:",t)}}function te(){document.querySelectorAll(".increment-btn").forEach(t=>{t.addEventListener("click",e=>{const n=parseInt(e.target.dataset.dhikr);U(n)})}),document.querySelectorAll(".reset-btn").forEach(t=>{t.addEventListener("click",e=>{const n=parseInt(e.target.dataset.dhikr);ie(n)})}),document.getElementById("reset-all").addEventListener("click",le),document.getElementById("settings-btn").addEventListener("click",oe),document.getElementById("close-settings").addEventListener("click",ue),document.getElementById("reminder-interval").addEventListener("change",ce),document.getElementById("always-on-top").addEventListener("change",de),document.getElementById("export-data").addEventListener("click",we),document.getElementById("import-data").addEventListener("click",ye),document.addEventListener("mouseenter",ae),document.addEventListener("mouseleave",re)}function ne(){document.addEventListener("keydown",t=>{if(t.key>="1"&&t.key<="4"){const e=parseInt(t.key);U(e),t.preventDefault()}})}function U(t){d[t]++,_(),w();const e=document.getElementById(`count-${t}`);e.classList.add("increment-animation"),setTimeout(()=>e.classList.remove("increment-animation"),300)}function ie(t){d[t]=0,_(),w()}function le(){confirm("Are you sure you want to reset all counters?")&&(d={1:0,2:0,3:0,4:0},_(),w())}function _(){for(let e=1;e<=4;e++)document.getElementById(`count-${e}`).textContent=d[e];const t=Object.values(d).reduce((e,n)=>e+n,0);Y.textContent=t,A.querySelector(".total-count").textContent=t}function se(){setTimeout(()=>{F()},2e3)}function ae(){p&&(clearTimeout(p),p=null),L&&(L=!1,A.classList.add("hidden"),M.classList.remove("hidden"))}function re(){p&&clearTimeout(p),p=setTimeout(()=>{F()},800)}function F(){!L&&!k.classList.contains("hidden")||(L=!0,A.classList.remove("hidden"),M.classList.add("hidden"))}function oe(){k.classList.remove("hidden")}function ue(){k.classList.add("hidden")}function G(){document.getElementById("reminder-interval").value=o.reminderIntervalMinutes,document.getElementById("always-on-top").checked=o.alwaysOnTop,o.reminderIntervalMinutes>0&&H()}function ce(t){o.reminderIntervalMinutes=parseInt(t.target.value),w(),b&&(clearInterval(b),b=null),o.reminderIntervalMinutes>0&&H()}function de(t){o.alwaysOnTop=t.target.checked,w(),P().setAlwaysOnTop(o.alwaysOnTop)}function H(){b&&clearInterval(b),b=setInterval(()=>{he()},o.reminderIntervalMinutes*60*1e3)}function he(){i("show_reminder_notification")}function we(){const t=JSON.stringify({counts:d,settings:o},null,2),e=new Blob([t],{type:"application/json"}),n=URL.createObjectURL(e),l=document.createElement("a");l.href=n,l.download="tasbeeh-data.json",l.click(),URL.revokeObjectURL(n)}function ye(){const t=document.createElement("input");t.type="file",t.accept=".json",t.onchange=e=>{const n=e.target.files[0];if(n){const l=new FileReader;l.onload=s=>{try{const a=JSON.parse(s.target.result);a.counts&&(d=a.counts),a.settings&&(o={...o,...a.settings}),_(),G(),w(),alert("Data imported successfully!")}catch{alert("Invalid file format")}},l.readAsText(n)}},t.click()}document.addEventListener("DOMContentLoaded",X);window.addEventListener("beforeunload",w);
