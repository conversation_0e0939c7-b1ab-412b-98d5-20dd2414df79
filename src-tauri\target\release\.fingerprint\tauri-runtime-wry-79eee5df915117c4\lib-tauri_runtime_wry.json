{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 2542800518684623442, "deps": [[376837177317575824, "softbuffer", false, 3979115511887932219], [2013030631243296465, "webview2_com", false, 2273413776130414245], [2671782512663819132, "tauri_utils", false, 8837733740804702724], [3150220818285335163, "url", false, 16112659691765740613], [3722963349756955755, "once_cell", false, 16169173815255071269], [4143744114649553716, "raw_window_handle", false, 8637821025176465503], [5986029879202738730, "log", false, 1886841086015139440], [6089812615193535349, "tauri_runtime", false, 13927800302588223230], [8826339825490770380, "tao", false, 16825907702907606036], [9010263965687315507, "http", false, 4214057606062284768], [9141053277961803901, "wry", false, 11554390921037776954], [11599800339996261026, "build_script_build", false, 14564979541697049432], [14585479307175734061, "windows", false, 7131688417191765701]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-79eee5df915117c4\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}