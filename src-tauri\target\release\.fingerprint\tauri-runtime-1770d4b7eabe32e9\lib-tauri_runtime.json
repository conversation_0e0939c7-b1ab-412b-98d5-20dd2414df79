{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 2092500432898867213, "deps": [[2671782512663819132, "tauri_utils", false, 8837733740804702724], [3150220818285335163, "url", false, 16112659691765740613], [4143744114649553716, "raw_window_handle", false, 8637821025176465503], [6089812615193535349, "build_script_build", false, 12002249198034878645], [7606335748176206944, "dpi", false, 16165669722731118951], [9010263965687315507, "http", false, 4214057606062284768], [9689903380558560274, "serde", false, 11263741590175630023], [10806645703491011684, "thiserror", false, 352052627267944730], [14585479307175734061, "windows", false, 7131688417191765701], [15367738274754116744, "serde_json", false, 1074150587366838657], [16727543399706004146, "cookie", false, 2676065732424466422]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-1770d4b7eabe32e9\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}